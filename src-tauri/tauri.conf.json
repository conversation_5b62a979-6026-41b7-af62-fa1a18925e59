{"productName": "switch-ai", "version": "0.1.0", "identifier": "com.switchai.app", "build": {"beforeDevCommand": "npm run dev", "beforeBuildCommand": "npm run build", "devUrl": "http://localhost:1420", "frontendDist": "../dist"}, "app": {"windows": [{"label": "main", "title": "Switch.AI", "width": 1200, "height": 800, "minWidth": 800, "minHeight": 600, "center": true, "resizable": true, "fullscreen": false, "decorations": true, "transparent": false, "alwaysOnTop": false, "skipTaskbar": false, "titleBarStyle": "Visible"}], "security": {"csp": {"default-src": ["'self'", "'unsafe-inline'", "'unsafe-eval'", "data:", "https:", "http:", "tauri:", "asset:"], "connect-src": ["'self'", "https:", "http:", "wss:", "ws:"], "script-src": ["'self'", "'unsafe-inline'", "'unsafe-eval'", "https:", "http:"], "style-src": ["'self'", "'unsafe-inline'", "https:", "http:"], "img-src": ["'self'", "data:", "https:", "http:", "asset:", "tauri:"], "font-src": ["'self'", "data:", "https:", "http:"], "media-src": ["'self'", "data:", "https:", "http:"], "frame-src": ["'self'", "https:", "http:"]}, "dangerousDisableAssetCspModification": true}}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"], "resources": [], "externalBin": [], "copyright": "", "category": "DeveloperTool", "shortDescription": "", "longDescription": "", "linux": {"deb": {"depends": []}}, "macOS": {"frameworks": [], "minimumSystemVersion": "", "exceptionDomain": "", "signingIdentity": null, "providerShortName": null, "entitlements": null}, "windows": {"certificateThumbprint": null, "digestAlgorithm": "sha256", "timestampUrl": ""}}, "plugins": {}}