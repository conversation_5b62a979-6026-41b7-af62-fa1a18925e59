import { useEffect, useRef } from 'react';
import { useAppStore } from '../store';
import { WebviewWindow } from '@tauri-apps/api/webviewWindow';
import { getCurrentWindow, LogicalSize, LogicalPosition } from '@tauri-apps/api/window';
import { UnlistenFn } from '@tauri-apps/api/event';
import { openUrl } from '@tauri-apps/plugin-opener';

const webviewInstances = new Map<string, WebviewWindow>();

async function hideAllWebviews() {
  for (const webview of webviewInstances.values()) {
    try {
      await webview.hide();
    } catch (error) {
      console.warn('Failed to hide webview:', error);
    }
  }
}

export const WebView = () => {
  const { activePlatform, activeMode } = useAppStore();
  const previousPlatformId = useRef<string | null>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  const updateWebviewGeometry = async () => {
    if (!activePlatform || !containerRef.current) return;
    const webview = webviewInstances.get(activePlatform.id);
    if (!webview) return;

    try {
      const rect = containerRef.current.getBoundingClientRect();
      const size = new LogicalSize(rect.width, rect.height);
      const position = new LogicalPosition(rect.left, rect.top);

      await webview.setSize(size);
      await webview.setPosition(position);
    } catch (error) {
      console.warn('Failed to update webview geometry:', error);
    }
  };

  useEffect(() => {
    const manageWebviews = async () => {
      if (previousPlatformId.current) {
        const prevWebview = webviewInstances.get(previousPlatformId.current);
        if (prevWebview) {
          try {
            await prevWebview.hide();
          } catch (error) {
            console.warn('Failed to hide previous webview:', error);
          }
        }
      }

      if (!activePlatform) {
        previousPlatformId.current = null;
        return;
      }

      const { id, url, name } = activePlatform;
      const label = `platform-${id}`;
      let webview = webviewInstances.get(id);

      if (webview) {
        try {
          await updateWebviewGeometry();
          await webview.show();
          await webview.setFocus();
        } catch (error) {
          console.warn('Failed to show existing webview:', error);
        }
      } else {
        try {
          webview = new WebviewWindow(label, {
            url,
            title: name,
            visible: false,
            decorations: false,
            skipTaskbar: true,
            resizable: true,
            transparent: false,
            alwaysOnTop: false,
            userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            acceptFirstMouse: true,
            tabbingIdentifier: label,
          });
          
          webviewInstances.set(id, webview);

          webview.once('tauri://created', async () => {
            try {
              await updateWebviewGeometry();
              if (webview) {
                await webview.show();
                await webview.setFocus();
              }
            } catch (error) {
              console.warn('Failed to setup new webview:', error);
            }
          });

          webview.once('tauri://error', (e: any) => {
            console.error(`Failed to create webview for ${name}:`, e);
            webviewInstances.delete(id);
          });

        } catch (error) {
          console.error('Failed to create webview:', error);
        }
      }

      previousPlatformId.current = id;
    };

    if (activeMode !== 'settings') {
      manageWebviews();
    }
  }, [activePlatform, activeMode]);

  useEffect(() => {
    if (activeMode === 'settings') {
      hideAllWebviews();
      previousPlatformId.current = null;
    }
  }, [activeMode]);

  useEffect(() => {
    let unlistenResize: UnlistenFn;
    let unlistenMove: UnlistenFn;

    const listenToWindowChanges = async () => {
      try {
        const currentWindow = getCurrentWindow();
        unlistenResize = await currentWindow.onResized(updateWebviewGeometry);
        unlistenMove = await currentWindow.onMoved(updateWebviewGeometry);
      } catch (error) {
        console.warn('Failed to setup window listeners:', error);
      }
    };

    listenToWindowChanges();

    const timer = setTimeout(() => {
      updateWebviewGeometry().catch(console.warn);
    }, 100);

    return () => {
      if (unlistenResize) unlistenResize();
      if (unlistenMove) unlistenMove();
      if (timer) clearTimeout(timer);
    };
  }, [activePlatform]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      webviewInstances.forEach(async (webview) => {
        try {
          await webview.close();
        } catch (error) {
          console.warn('Failed to close webview on cleanup:', error);
        }
      });
      webviewInstances.clear();
    };
  }, []);

  const EmptyState = () => (
    <div className="flex-1 flex items-center justify-center bg-gradient-to-br from-bgPrimary to-bgTertiary h-full">
      <div className="text-center space-y-6 max-w-md mx-auto px-6">
        <div className="w-24 h-24 mx-auto bg-gradient-to-br from-accentStart to-accentEnd rounded-3xl flex items-center justify-center shadow-xl">
          <span className="text-white text-2xl font-bold">AI</span>
        </div>
        <div className="space-y-3">
          <h3 className="text-2xl font-bold text-textPrimary">Select a Platform</h3>
          <p className="text-textSecondary leading-relaxed">
            Choose a platform from the sidebar to begin.
          </p>
        </div>
        {activePlatform && (
          <button
            onClick={() => openUrl(activePlatform.url)}
            className="px-6 py-3 bg-gradient-to-r from-accentStart to-accentEnd hover:from-accentHover hover:to-accentStart text-white rounded-xl transition-all duration-200 font-medium shadow-lg hover:shadow-xl hover:scale-105"
          >
            Open {activePlatform.name} in Browser
          </button>
        )}
      </div>
    </div>
  );

  return (
    <div ref={containerRef} className="flex-1 relative">
      {!activePlatform && <EmptyState />}
    </div>
  );
};
