import { useAppStore } from '../store';

const modes = [
  { key: 'chat', label: 'Chat' },
  { key: 'create', label: 'Create' },
  { key: 'lab', label: 'Lab' },
  { key: 'hub', label: 'Hub' },
  { key: 'settings', label: 'Settings' }
] as const;

export const TopBar = () => {
  const { activeMode, setMode } = useAppStore();

  return (
    <div
      data-tauri-drag-region
      className="h-16 flex items-center justify-between px-6 border-b border-border bg-bgPrimary/80 backdrop-blur-xl flex-shrink-0"
    >
      {/* Logo */}
      <div className="flex items-center">
        <svg
          width="200"
          height="40"
          viewBox="0 0 388 74"
          xmlns="http://www.w3.org/2000/svg"
          className="h-10 w-auto"
        >
          <defs>
            <style>
              {`.st0 { fill: #f0ec76; }`}
            </style>
          </defs>
          <path className="st0" d="M15.21,60.73c-2.33,0-3.72-1.35-3.72-3.61s1.39-3.61,3.72-3.61h28.61c1.31,0,2.43-.33,3.44-1.02.97-.67,1.42-1.44,1.42-2.43v-5.28c0-.99-.46-1.76-1.43-2.44-1-.68-2.12-1.01-3.43-1.01h-20.06c-3.19,0-6.01-.97-8.36-2.88-2.59-2.08-3.91-4.71-3.91-7.83v-5.24c0-3.11,1.32-5.75,3.91-7.83,2.36-1.88,5.17-2.84,8.35-2.84h20.06c2.33,0,3.72,1.35,3.72,3.61s-1.39,3.61-3.72,3.61h-20.06c-1.28,0-2.39.33-3.4,1.02-1.27.9-1.42,1.82-1.42,2.43v5.24c0,.61.15,1.53,1.45,2.45,1,.7,2.1,1.04,3.38,1.04h20.06c3.19,0,6.01.95,8.38,2.83,2.6,2.09,3.92,4.72,3.92,7.84v5.28c0,3.17-1.36,5.84-4.04,7.92-2.38,1.83-5.16,2.75-8.26,2.75H15.21Z"/>
          <path className="st0" d="M106.38,60.73c-1.64,0-2.88-.9-3.48-2.52l-11.38-29.74-11.38,29.75c-.6,1.61-1.84,2.51-3.48,2.51s-2.92-.9-3.52-2.52l-14.86-38.85c-.16-.39-.24-.83-.24-1.29,0-2.24,1.4-3.57,3.76-3.57,1.63,0,2.86.84,3.44,2.36l11.42,29.73,11.38-29.73c.63-1.64,1.87-2.55,3.48-2.55s2.84.9,3.48,2.54l11.38,29.72,11.38-29.72c.59-1.52,1.81-2.36,3.44-2.36,2.38,0,3.79,1.34,3.79,3.57,0,.37-.09.78-.27,1.29l-14.87,38.85c-.6,1.61-1.84,2.51-3.48,2.51Z"/>
          <path className="st0" d="M135.64,60.73c-2.33,0-3.72-1.35-3.72-3.61V18.33c0-2.26,1.39-3.61,3.72-3.61s3.72,1.35,3.72,3.61v38.79c0,2.26-1.39,3.61-3.72,3.61Z"/>
          <path className="st0" d="M168.58,60.73c-2.33,0-3.72-1.35-3.72-3.61V21.94h-14.85c-2.33,0-3.72-1.35-3.72-3.61s1.39-3.61,3.72-3.61h37.19c2.33,0,3.72,1.35,3.72,3.61s-1.39,3.61-3.72,3.61h-14.89v35.18c0,2.26-1.39,3.61-3.72,3.61Z"/>
          <path className="st0" d="M209.1,60.73c-3.19,0-6-.95-8.35-2.84-2.6-2.08-3.91-4.72-3.91-7.83v-24.63c0-3.14,1.3-5.77,3.88-7.83,2.38-1.91,5.2-2.87,8.39-2.87h20.06c2.33,0,3.72,1.35,3.72,3.61s-1.39,3.61-3.72,3.61h-20.06c-1.29,0-2.4.34-3.38,1.04-.99.69-1.45,1.46-1.45,2.45v24.63c0,.61.15,1.53,1.45,2.45.98.67,2.1,1,3.38,1h28.64c2.33,0,3.72,1.35,3.72,3.61s-1.39,3.61-3.72,3.61h-28.64Z"/>
          <path className="st0" d="M288.3,60.73c-2.33,0-3.72-1.35-3.72-3.61v-21.06h-29.75v21.06c0,2.26-1.39,3.61-3.72,3.61s-3.72-1.35-3.72-3.61V18.33c0-2.26,1.39-3.61,3.72-3.61s3.72,1.35,3.72,3.61v10.55h29.75v-10.55c0-2.26,1.39-3.61,3.72-3.61s3.72,1.35,3.72,3.61v38.79c0,2.26-1.39,3.61-3.72,3.61Z"/>
          <path className="st0" d="M361.97,60.73c-1.51,0-2.7-.74-3.33-2.08l-5.78-12.04h-18.91l-5.75,12.05c-.63,1.33-1.82,2.07-3.36,2.07-2.35,0-3.76-1.33-3.76-3.57,0-.54.12-1.07.37-1.56l18.57-38.8c.67-1.45,1.88-2.25,3.4-2.25s2.69.81,3.37,2.27l18.57,38.8c.24.47.36,1,.36,1.54,0,2.24-1.4,3.57-3.76,3.57ZM349.43,39.39l-6-12.54-6.03,12.54h12.03Z"/>
          <path className="st0" d="M372.37,60.73c-2.33,0-3.72-1.35-3.72-3.61V18.33c0-2.26,1.39-3.61,3.72-3.61s3.72,1.35,3.72,3.61v38.79c0,2.26-1.39,3.61-3.72,3.61Z"/>
        </svg>
      </div>

      {/* Button Group Navigation */}
      <nav className="flex items-center bg-bgSecondary p-1 rounded-lg border border-border">
        {modes.map((mode) => {
          const isActive = activeMode === mode.key;
          return (
            <button
              key={mode.key}
              onClick={() => setMode(mode.key)}
              className={`px-4 py-1.5 text-sm font-semibold rounded-md transition-all duration-300 focus:outline-none focus-visible:ring-2 focus-visible:ring-accentStart focus-visible:ring-opacity-50 ${
                isActive
                  ? 'bg-bgTertiary shadow-sm'
                  : 'bg-transparent hover:opacity-80'
              }`}
              style={{ color: '#c6c997' }}
            >
              {mode.label}
            </button>
          );
        })}
      </nav>

      {/* Empty div to balance layout */}
      <div className="w-24" />
    </div>
  );
};
